# SDL Platform Docker 部署指南

## 概述

SDL Platform 采用宿主机构建 + Docker部署的方案：
- 在宿主机上编译前后端代码
- 使用两个Docker容器分别部署前端和后端
- 连接外部MySQL和Redis服务

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────────┐
│ 前端容器(Nginx)  │    │ 后端容器(Java)   │    │  外部数据库(MySQL)  │
│     Port: 80    │────│    Port: 8080   │────│ *************:3306 │
└─────────────────┘    └─────────────────┘    └─────────────────────┘
                              │
                       ┌─────────────────────┐
                       │   外部缓存(Redis)   │
                       │ *************:6379 │
                       └─────────────────────┘
```

## 系统要求

### 宿主机环境
- **Java**: JDK 17+
- **Maven**: 3.6+
- **Node.js**: 16+
- **pnpm**: 自动安装（如果未安装）
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

### 外部服务
- **MySQL**: *************:3306 (可访问)
- **Redis**: *************:6379 (可访问)

### Docker镜像
- **eclipse-temurin:17-jre** (后端Java运行时)
- **nginx:alpine** (前端Web服务器)

## 快速开始

### 一键部署
```bash
# 给脚本执行权限
chmod +x *.sh

# 一键构建和部署
./start.sh

# 或强制重新构建
./start.sh --rebuild
```

### 分步部署

1. **构建应用**
   ```bash
   ./build.sh
   ```

2. **部署到Docker**
   ```bash
   ./deploy.sh
   ```

## 详细步骤

### 步骤1: 环境检查

```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version

# 检查Node.js版本
node -v

# 检查pnpm版本（如果未安装会自动安装）
pnpm -v

# 检查Docker版本
docker --version
docker-compose --version
```

### 步骤2: 验证外部服务

```bash
# 测试MySQL连接
mysql -h ************* -P 3306 -u root -p

# 测试Redis连接
redis-cli -h ************* -p 6379 ping

# 或使用telnet测试端口
telnet ************* 3306
telnet ************* 6379
```

### 步骤3: 构建应用

```bash
# 运行构建脚本
./build.sh
```

构建过程包括：
- 后端Maven编译打包
- 前端pnpm构建（包含缓存清理）
- 复制构建产物到docker/deploy目录

### 步骤4: Docker部署

```bash
# 运行部署脚本
./deploy.sh
```

部署过程包括：
- 检查构建产物
- 构建Docker镜像
- 启动Docker容器
- 健康检查

## 文件结构

```
sdl-platform/
├── build.sh                    # 宿主机构建脚本
├── deploy.sh                   # Docker部署脚本
├── start.sh                    # 一键部署脚本
├── docker-compose.yml          # Docker编排文件
└── docker/
    ├── README.md               # 本文档
    ├── backend.Dockerfile      # 后端镜像构建文件
    ├── frontend.Dockerfile     # 前端镜像构建文件
    ├── nginx.conf              # Nginx配置文件
    └── deploy/                 # 构建产物目录(自动生成)
        ├── backend/
        │   └── sdl-platform-admin.jar
        └── frontend/
            ├── index.html
            └── ...
```

## 访问地址

- **前端**: http://localhost
- **后端API**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger-ui.html
- **Druid监控**: http://localhost:8080/druid

## 默认账号

- **用户名**: admin
- **密码**: admin123

## 常用命令

### 服务管理
```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f
docker-compose logs -f sdl-backend
docker-compose logs -f sdl-frontend

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 重新部署
./start.sh --rebuild
```

### 开发调试
```bash
# 只重新构建后端
mvn clean package -DskipTests
cp sdl-platform-admin/target/sdl-platform-admin.jar docker/deploy/backend/
docker-compose restart sdl-backend

# 只重新构建前端
cd sdl-platform-vue3
pnpm run build:prod
cd ..
cp -r sdl-platform-vue3/dist/* docker/deploy/frontend/
docker-compose restart sdl-frontend
```

## 故障排除

### 构建问题

1. **Maven构建失败**
   ```bash
   # 清理并重试
   mvn clean
   mvn package -DskipTests -X
   ```

2. **pnpm构建失败**
   ```bash
   # 清理缓存并重新安装
   cd sdl-platform-vue3
   rm -rf node_modules pnpm-lock.yaml
   pnpm store prune
   pnpm install
   pnpm run build:prod
   ```

3. **缺失依赖问题（如sortablejs）**
   ```bash
   # 手动安装缺失的依赖
   cd sdl-platform-vue3
   pnpm add sortablejs
   pnpm run build:prod
   ```

### 部署问题

1. **Docker镜像构建失败**
   ```bash
   # 查看详细构建日志
   docker-compose build --no-cache
   ```

2. **服务启动失败**
   ```bash
   # 查看容器日志
   docker-compose logs sdl-backend
   docker-compose logs sdl-frontend
   ```

3. **外部服务连接失败**
   ```bash
   # 检查网络连通性
   ping *************
   telnet ************* 3306
   telnet ************* 6379
   ```

### 清理和重置

```bash
# 完全清理
docker-compose down
docker rmi $(docker images | grep sdl | awk '{print $3}') 2>/dev/null || true
rm -rf docker/deploy
./start.sh --rebuild
```

## 注意事项

1. **构建环境**: 确保宿主机有完整的Java和Node.js开发环境
2. **外部服务**: 确保能够访问外部MySQL和Redis服务
3. **端口占用**: 确保80和8080端口未被占用
4. **Docker镜像**: 确保可以拉取eclipse-temurin:17-jre和nginx:alpine镜像

---

如有问题，请检查日志并参考故障排除部分。
